import React from "react";
import { Link, useLocation } from "react-router-dom";
import logoSvg from "../assets/logo.svg";

const Navbar: React.FC = () => {
  const location = useLocation();

  const isActive = (path: string) => location.pathname === path;

  return (
    <header style={styles.header} className="navbar-header">
      <Link to="/" style={styles.logoLink}>
        <div style={styles.logo} className="navbar-logo">
          <img
            src={logoSvg}
            alt="Sport Sphere Logo"
            style={styles.logoImg}
            className="navbar-logo-img"
          />
          <div style={styles.logoText} className="navbar-logo-text">
            Sport Sphere
          </div>
        </div>
      </Link>

      <nav style={styles.nav} className="navbar-nav">
        <Link
          to="/"
          style={{
            ...styles.navLink,
            ...(isActive("/") ? styles.navLinkActive : {}),
          }}
          className="navbar-link"
        >
          Home
        </Link>
        <Link
          to="/find-coaches"
          style={{
            ...styles.navLink,
            ...(isActive("/find-coaches") ? styles.navLinkActive : {}),
          }}
          className="navbar-link"
        >
          Find Coaches
        </Link>
        <Link
          to="/marketPlace"
          style={{
            ...styles.navLink,
            ...(isActive("/marketPlace") ? styles.navLinkActive : {}),
          }}
          className="navbar-link"
        >
          Marketplace
        </Link>
        <Link
          to="/login"
          style={{
            ...styles.navLink,
            ...styles.loginBtn,
            ...(isActive("/login") ? styles.navLinkActive : {}),
          }}
          className="navbar-link navbar-login"
        >
          Login
        </Link>
        <Link
          to="/register"
          style={{
            ...styles.navLink,
            ...styles.registerBtn,
            ...(isActive("/register") ? styles.navLinkActive : {}),
          }}
          className="navbar-link navbar-register"
        >
          Sign Up
        </Link>
      </nav>
    </header>
  );
};

const styles = {
  header: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    padding: "1rem 5%",
    backgroundColor: "rgba(255, 255, 255, 0.95)",
    position: "fixed" as const,
    width: "100%",
    top: 0,
    left: 0,
    zIndex: 1000,
    boxShadow: "0 2px 20px rgba(0, 0, 0, 0.1)",
    backdropFilter: "blur(10px)",
    borderBottom: "1px solid rgba(255, 255, 255, 0.2)",
  },
  logoLink: {
    textDecoration: "none",
    color: "inherit",
  },
  logo: {
    display: "flex",
    alignItems: "center",
    gap: "12px",
    transition: "transform 0.3s ease",
  },
  logoImg: {
    height: "45px",
    width: "45px",
    filter: "drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1))",
  },
  logoText: {
    fontSize: "1.8rem",
    fontWeight: "bold" as const,
    color: "#2c3e50",
    fontStyle: "italic" as const,
    background: "linear-gradient(135deg, #2c3e50, #3498db)",
    WebkitBackgroundClip: "text",
    WebkitTextFillColor: "transparent",
    backgroundClip: "text",
  },
  nav: {
    display: "flex",
    alignItems: "center",
    gap: "2rem",
  },
  navLink: {
    textDecoration: "none",
    color: "#2c3e50",
    fontWeight: "600" as const,
    fontSize: "1rem",
    padding: "0.5rem 1rem",
    borderRadius: "8px",
    transition: "all 0.3s ease",
    position: "relative" as const,
  },
  navLinkActive: {
    color: "#e74c3c",
    backgroundColor: "rgba(231, 76, 60, 0.1)",
  },
  loginBtn: {
    border: "2px solid #3498db",
    color: "#3498db",
    backgroundColor: "transparent",
  },
  registerBtn: {
    backgroundColor: "#e74c3c",
    color: "white",
    border: "2px solid #e74c3c",
  },
};

// Add hover effects with CSS-in-JS
const hoverStyles = `
  .navbar-link:hover {
    color: #e74c3c !important;
    transform: translateY(-2px);
    background-color: rgba(231, 76, 60, 0.1) !important;
  }
  
  .navbar-login:hover {
    background-color: #3498db !important;
    color: white !important;
    transform: translateY(-2px);
  }
  
  .navbar-register:hover {
    background-color: #c0392b !important;
    border-color: #c0392b !important;
    transform: translateY(-2px);
  }
  
  .navbar-logo:hover {
    transform: scale(1.05);
  }
  
  @media (max-width: 768px) {
    .navbar-header {
      padding: 0.8rem 3% !important;
      flex-wrap: wrap;
    }
    
    .navbar-logo-img {
      height: 35px !important;
      width: 35px !important;
    }
    
    .navbar-logo-text {
      font-size: 1.5rem !important;
    }
    
    .navbar-nav {
      gap: 1rem !important;
      flex-wrap: wrap;
    }
    
    .navbar-link {
      font-size: 0.9rem !important;
      padding: 0.4rem 0.8rem !important;
    }
  }
  
  @media (max-width: 480px) {
    .navbar-header {
      flex-direction: column;
      gap: 1rem;
      padding: 1rem 3% !important;
    }
    
    .navbar-nav {
      justify-content: center;
      width: 100%;
    }
    
    .navbar-logo-text {
      font-size: 1.3rem !important;
    }
  }
`;

// Inject styles
if (typeof document !== "undefined") {
  const styleElement = document.createElement("style");
  styleElement.textContent = hoverStyles;
  document.head.appendChild(styleElement);
}

export default Navbar;

const Join<PERSON><PERSON>thlete = () => {
  return (
    <div style={styles.container} className="athlete-container">
      <div style={styles.content} className="athlete-content">
        <div style={styles.header}>
          <h1 style={styles.title} className="athlete-title">
            Join as Athlete
          </h1>
          <p style={styles.subtitle} className="athlete-subtitle">
            Start your journey to athletic excellence with professional coaching
          </p>
        </div>

        <div style={styles.card} className="athlete-card">
          <form style={styles.form} className="athlete-form">
            <div style={styles.formGroup}>
              <label style={styles.label}>Full Name</label>
              <input
                type="text"
                style={styles.input}
                className="athlete-input"
                placeholder="Enter your full name"
                required
              />
            </div>

            <div style={styles.formGroup}>
              <label style={styles.label}>Email</label>
              <input
                type="email"
                style={styles.input}
                placeholder="Enter your email"
                required
              />
            </div>

            <div style={styles.formGroup}>
              <label style={styles.label}>Phone Number</label>
              <input
                type="tel"
                style={styles.input}
                placeholder="Enter your phone number"
                required
              />
            </div>

            <div style={styles.formGroup}>
              <label style={styles.label}>Sport of Interest</label>
              <select style={styles.input} required>
                <option value="">Select your sport</option>
                <option value="tennis">Tennis</option>
                <option value="basketball">Basketball</option>
                <option value="soccer">Soccer</option>
                <option value="swimming">Swimming</option>
                <option value="golf">Golf</option>
                <option value="volleyball">Volleyball</option>
                <option value="cycling">Cycling</option>
                <option value="other">Other</option>
              </select>
            </div>

            <div style={styles.formGroup}>
              <label style={styles.label}>Experience Level</label>
              <select style={styles.input} required>
                <option value="">Select your level</option>
                <option value="beginner">Beginner</option>
                <option value="intermediate">Intermediate</option>
                <option value="advanced">Advanced</option>
                <option value="professional">Professional</option>
              </select>
            </div>

            <div style={styles.formGroup}>
              <label style={styles.label}>Goals</label>
              <textarea
                style={styles.textarea}
                className="athlete-textarea"
                placeholder="Tell us about your athletic goals..."
                rows={4}
              />
            </div>

            <button type="submit" style={styles.submitBtn}>
              Join as Athlete
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

const styles = {
  container: {
    minHeight: "100vh",
    background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
    padding: "2rem",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
  },
  content: {
    width: "100%",
    maxWidth: "600px",
    margin: "0 auto",
  },
  header: {
    textAlign: "center" as const,
    marginBottom: "2rem",
    color: "white",
  },
  title: {
    fontSize: "2.5rem",
    fontWeight: "bold" as const,
    marginBottom: "0.5rem",
    textShadow: "0 2px 4px rgba(0,0,0,0.3)",
  },
  subtitle: {
    fontSize: "1.2rem",
    opacity: 0.9,
  },
  card: {
    backgroundColor: "white",
    borderRadius: "12px",
    padding: "2rem",
    boxShadow: "0 10px 30px rgba(0,0,0,0.2)",
  },
  form: {
    display: "flex",
    flexDirection: "column" as const,
    gap: "1.5rem",
  },
  formGroup: {
    display: "flex",
    flexDirection: "column" as const,
    gap: "0.5rem",
  },
  label: {
    fontSize: "1rem",
    fontWeight: "600" as const,
    color: "#333",
  },
  input: {
    padding: "0.75rem",
    border: "2px solid #e1e5e9",
    borderRadius: "8px",
    fontSize: "1rem",
    transition: "border-color 0.3s ease",
    outline: "none",
  },
  textarea: {
    padding: "0.75rem",
    border: "2px solid #e1e5e9",
    borderRadius: "8px",
    fontSize: "1rem",
    transition: "border-color 0.3s ease",
    outline: "none",
    resize: "vertical" as const,
    fontFamily: "inherit",
  },
  submitBtn: {
    backgroundColor: "#667eea",
    color: "white",
    padding: "1rem 2rem",
    border: "none",
    borderRadius: "8px",
    fontSize: "1.1rem",
    fontWeight: "600" as const,
    cursor: "pointer",
    transition: "background-color 0.3s ease",
    marginTop: "1rem",
  },
};

// Add hover effects and mobile responsiveness
const hoverStyles = `
  input:focus, textarea:focus, select:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
  }

  button:hover {
    background-color: #5a6fd8 !important;
    transform: translateY(-2px);
  }

  @media (max-width: 768px) {
    .athlete-container {
      padding: 1rem !important;
    }

    .athlete-content {
      max-width: 100% !important;
    }

    .athlete-card {
      padding: 1.5rem !important;
      margin: 0 0.5rem !important;
    }

    .athlete-title {
      font-size: 2rem !important;
    }

    .athlete-subtitle {
      font-size: 1rem !important;
    }
  }

  @media (max-width: 480px) {
    .athlete-container {
      padding: 0.5rem !important;
    }

    .athlete-card {
      padding: 1rem !important;
      margin: 0 !important;
    }

    .athlete-title {
      font-size: 1.8rem !important;
    }

    .athlete-subtitle {
      font-size: 0.95rem !important;
    }

    .athlete-form {
      gap: 1rem !important;
    }

    .athlete-input, .athlete-textarea {
      font-size: 16px !important; /* Prevents zoom on iOS */
    }
  }
`;

// Inject styles
if (typeof document !== "undefined") {
  const styleElement = document.createElement("style");
  styleElement.textContent = hoverStyles;
  document.head.appendChild(styleElement);
}

export default JoinAsAthlete;

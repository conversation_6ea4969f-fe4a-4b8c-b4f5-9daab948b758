import React, { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import logoSvg from "../assets/logo.svg";

const Navbar: React.FC = () => {
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const isActive = (path: string) => location.pathname === path;

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  return (
    <header style={styles.header} className="navbar-header">
      <Link to="/" style={styles.logoLink}>
        <div style={styles.logo} className="navbar-logo">
          <img
            src={logoSvg}
            alt="Sport Sphere Logo"
            style={styles.logoImg}
            className="navbar-logo-img"
          />
          <div style={styles.logoText} className="navbar-logo-text">
            Sport Sphere
          </div>
        </div>
      </Link>

      <button
        style={styles.hamburger}
        className="navbar-hamburger"
        onClick={toggleMobileMenu}
        aria-label="Toggle navigation menu"
      >
        <span style={styles.hamburgerLine}></span>
        <span style={styles.hamburgerLine}></span>
        <span style={styles.hamburgerLine}></span>
      </button>

      {/* Desktop Navigation */}
      <nav style={styles.nav} className="navbar-nav navbar-desktop">
        <Link
          to="/"
          style={{
            ...styles.navLink,
            ...(isActive("/") ? styles.navLinkActive : {}),
          }}
          className="navbar-link"
        >
          Home
        </Link>
        <Link
          to="/find-coaches"
          style={{
            ...styles.navLink,
            ...(isActive("/find-coaches") ? styles.navLinkActive : {}),
          }}
          className="navbar-link"
        >
          Find Coaches
        </Link>
        <Link
          to="/marketPlace"
          style={{
            ...styles.navLink,
            ...(isActive("/marketPlace") ? styles.navLinkActive : {}),
          }}
          className="navbar-link"
        >
          Marketplace
        </Link>
        <Link
          to="/login"
          style={{
            ...styles.navLink,
            ...styles.loginBtn,
            ...(isActive("/login") ? styles.navLinkActive : {}),
          }}
          className="navbar-link navbar-login"
        >
          Login
        </Link>
        <Link
          to="/register"
          style={{
            ...styles.navLink,
            ...styles.registerBtn,
            ...(isActive("/register") ? styles.navLinkActive : {}),
          }}
          className="navbar-link navbar-register"
        >
          Sign Up
        </Link>
      </nav>

      {/* Mobile Navigation */}
      {isMobileMenuOpen && (
        <nav style={styles.mobileNav} className="navbar-mobile">
          <Link
            to="/"
            style={{
              ...styles.mobileNavLink,
              ...(isActive("/") ? styles.navLinkActive : {}),
            }}
            className="navbar-mobile-link"
            onClick={closeMobileMenu}
          >
            Home
          </Link>
          <Link
            to="/find-coaches"
            style={{
              ...styles.mobileNavLink,
              ...(isActive("/find-coaches") ? styles.navLinkActive : {}),
            }}
            className="navbar-mobile-link"
            onClick={closeMobileMenu}
          >
            Find Coaches
          </Link>
          <Link
            to="/marketPlace"
            style={{
              ...styles.mobileNavLink,
              ...(isActive("/marketPlace") ? styles.navLinkActive : {}),
            }}
            className="navbar-mobile-link"
            onClick={closeMobileMenu}
          >
            Marketplace
          </Link>
          <Link
            to="/login"
            style={{
              ...styles.mobileNavLink,
              ...styles.mobileLoginBtn,
              ...(isActive("/login") ? styles.navLinkActive : {}),
            }}
            className="navbar-mobile-link navbar-mobile-login"
            onClick={closeMobileMenu}
          >
            Login
          </Link>
          <Link
            to="/register"
            style={{
              ...styles.mobileNavLink,
              ...styles.mobileRegisterBtn,
              ...(isActive("/register") ? styles.navLinkActive : {}),
            }}
            className="navbar-mobile-link navbar-mobile-register"
            onClick={closeMobileMenu}
          >
            Sign Up
          </Link>
        </nav>
      )}
    </header>
  );
};

const styles = {
  header: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    padding: "1rem 5%",
    backgroundColor: "rgba(255, 255, 255, 0.95)",
    position: "fixed" as const,
    width: "100%",
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
    boxShadow: "0 2px 20px rgba(0, 0, 0, 0.1)",
    backdropFilter: "blur(10px)",
    borderBottom: "1px solid rgba(255, 255, 255, 0.2)",
    boxSizing: "border-box" as const,
  },
  logoLink: {
    textDecoration: "none",
    color: "inherit",
  },
  logo: {
    display: "flex",
    alignItems: "center",
    gap: "12px",
    transition: "transform 0.3s ease",
  },
  logoImg: {
    height: "45px",
    width: "45px",
    filter: "drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1))",
  },
  logoText: {
    fontSize: "1.8rem",
    fontWeight: "bold" as const,
    color: "#2c3e50",
    fontStyle: "italic" as const,
    background: "linear-gradient(135deg, #2c3e50, #3498db)",
    WebkitBackgroundClip: "text",
    WebkitTextFillColor: "transparent",
    backgroundClip: "text",
  },
  nav: {
    display: "flex",
    alignItems: "center",
    gap: "1.5rem",
    flexShrink: 0,
    minWidth: "fit-content",
  },
  navLink: {
    textDecoration: "none",
    color: "#2c3e50",
    fontWeight: "600" as const,
    fontSize: "1rem",
    padding: "0.5rem 1rem",
    borderRadius: "8px",
    transition: "all 0.3s ease",
    position: "relative" as const,
    whiteSpace: "nowrap" as const,
  },
  navLinkActive: {
    color: "#e74c3c",
    backgroundColor: "rgba(231, 76, 60, 0.1)",
  },
  loginBtn: {
    border: "2px solid #3498db",
    color: "#3498db",
    backgroundColor: "transparent",
  },
  registerBtn: {
    backgroundColor: "#e74c3c",
    color: "white",
    border: "2px solid #e74c3c",
  },
  hamburger: {
    display: "none",
    flexDirection: "column" as const,
    justifyContent: "space-around",
    width: "30px",
    height: "30px",
    background: "transparent",
    border: "none",
    cursor: "pointer",
    padding: "0",
    zIndex: 1001,
  },
  hamburgerLine: {
    width: "100%",
    height: "3px",
    backgroundColor: "#2c3e50",
    borderRadius: "2px",
    transition: "all 0.3s ease",
  },
  mobileNav: {
    position: "absolute" as const,
    top: "100%",
    left: 0,
    right: 0,
    backgroundColor: "rgba(255, 255, 255, 0.98)",
    backdropFilter: "blur(10px)",
    boxShadow: "0 4px 20px rgba(0, 0, 0, 0.1)",
    display: "flex",
    flexDirection: "column" as const,
    padding: "1rem 5%",
    gap: "0.5rem",
    zIndex: 999,
  },
  mobileNavLink: {
    textDecoration: "none",
    color: "#2c3e50",
    fontWeight: "600" as const,
    fontSize: "1.1rem",
    padding: "1rem",
    borderRadius: "8px",
    transition: "all 0.3s ease",
    textAlign: "center" as const,
  },
  mobileLoginBtn: {
    border: "2px solid #3498db",
    color: "#3498db",
    backgroundColor: "transparent",
    marginTop: "0.5rem",
  },
  mobileRegisterBtn: {
    backgroundColor: "#e74c3c",
    color: "white",
    border: "2px solid #e74c3c",
  },
};

//i added a over effect just for a bit better visualization
const hoverStyles = `
  .navbar-header {
    box-sizing: border-box;
  }

  .navbar-link:hover {
    color: #e74c3c !important;
    transform: translateY(-2px);
    background-color: rgba(231, 76, 60, 0.1) !important;
  }

  .navbar-login:hover {
    background-color: #3498db !important;
    color: white !important;
    transform: translateY(-2px);
  }

  .navbar-register:hover {
    background-color: #c0392b !important;
    border-color: #c0392b !important;
    transform: translateY(-2px);
  }

  .navbar-logo:hover {
    transform: scale(1.05);
  }

  /* Desktop navigation visible by default */
  .navbar-desktop {
    display: flex;
  }

  .navbar-hamburger {
    display: none;
  }

  .navbar-mobile-link:hover {
    background-color: rgba(231, 76, 60, 0.1) !important;
    color: #e74c3c !important;
  }

  .navbar-mobile-login:hover {
    background-color: #3498db !important;
    color: white !important;
  }

  .navbar-mobile-register:hover {
    background-color: #c0392b !important;
    border-color: #c0392b !important;
  }

  @media (max-width: 1024px) {
    .navbar-header {
      padding: 1rem 3% !important;
    }

    .navbar-nav {
      gap: 1rem !important;
    }

    .navbar-link {
      font-size: 0.95rem !important;
      padding: 0.45rem 0.9rem !important;
    }
  }

  @media (max-width: 768px) {
    .navbar-header {
      padding: 0.8rem 3% !important;
    }

    .navbar-logo-img {
      height: 35px !important;
      width: 35px !important;
    }

    .navbar-logo-text {
      font-size: 1.5rem !important;
    }

    /* Hide desktop navigation */
    .navbar-desktop {
      display: none !important;
    }

    /* Show hamburger menu */
    .navbar-hamburger {
      display: flex !important;
    }
  }
  
  @media (max-width: 480px) {
    .navbar-header {
      padding: 0.8rem 4% !important;
    }

    .navbar-logo-img {
      height: 30px !important;
      width: 30px !important;
    }

    .navbar-logo-text {
      font-size: 1.3rem !important;
    }

    .navbar-hamburger {
      width: 25px !important;
      height: 25px !important;
    }

    .navbar-mobile {
      padding: 0.8rem 4% !important;
    }

    .navbar-mobile-link {
      font-size: 1rem !important;
      padding: 0.8rem !important;
    }
  }
`;

// Inject styles
if (typeof document !== "undefined") {
  const styleElement = document.createElement("style");
  styleElement.textContent = hoverStyles;
  document.head.appendChild(styleElement);
}

export default Navbar;

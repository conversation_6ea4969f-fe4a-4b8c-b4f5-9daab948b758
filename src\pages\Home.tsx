import { Link } from "react-router-dom";

const HomePage = () => {
  return (
    <div style={styles.body}>
      {/* Hero Section */}
      <section style={styles.hero} className="hero-section">
        <h2 style={styles.heroH2} className="hero-title">
          Train Smarter. Play Harder.
        </h2>
        <p style={styles.heroP} className="hero-subtitle">
          Connect with professional coaches, track your progress, and explore
          verified sports gear.
        </p>
        <div style={styles.ctaButtons} className="cta-buttons">
          <Link
            to="/join-as-athlete"
            className="btn primary cta-button"
            style={{ ...styles.btn, ...styles.btnPrimary }}
          >
            <i className="fas fa-user" style={styles.icon}></i> Join as Athlete
          </Link>
          <Link
            to="/become-a-coach"
            className="btn secondary cta-button"
            style={{ ...styles.btn, ...styles.btnSecondary }}
          >
            <i className="fas fa-chalkboard-teacher" style={styles.icon}></i>{" "}
            Become a Coach
          </Link>
          <Link
            to="/join-as-vendor"
            className="btn tertiary cta-button"
            style={{ ...styles.btn, ...styles.btnTertiary }}
          >
            <i className="fas fa-store" style={styles.icon}></i> Join as Vendor
          </Link>
        </div>
      </section>

      {/* About Section */}
      <section style={styles.infoSection} className="info-section" id="about">
        <h3 style={styles.sectionTitle} className="section-title">
          Why Choose Sport Sphere?
        </h3>
        <ul style={styles.ul} className="features-grid">
          {[
            "Personalized coaching sessions with certified professionals",
            "Real-time progress tracking with advanced analytics",
            "Secure payment processing and encrypted messaging",
            "Authentic sports gear marketplace with verified vendors",
            "Comprehensive athlete development programs",
            "Flexible scheduling and session management",
          ].map((item, index) => (
            <li key={index} style={styles.li} className="feature-item">
              {item}
            </li>
          ))}
        </ul>
      </section>

      {/* Footer */}
      <footer style={styles.footer}>
        <p>&copy; 2025 Sport Sphere | All rights reserved</p>
      </footer>

      {/* Scoped Styles */}
      <style
        dangerouslySetInnerHTML={{
          __html: `
          @import url("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css");

          /* Animations */
          @keyframes fadeIn {
            from {
              opacity: 0;
              transform: translateY(20px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }

          /* Mobile responsive styles */
          @media (max-width: 768px) {
            .hero-section {
              padding: 0 3% !important;
              min-height: 90vh !important;
            }

            .hero-title {
              font-size: 2.2rem !important;
              margin-bottom: 1rem !important;
            }

            .hero-subtitle {
              font-size: 1rem !important;
              margin-bottom: 2rem !important;
            }

            .cta-buttons {
              flex-direction: column !important;
              gap: 1rem !important;
              align-items: center !important;
            }

            .cta-button {
              width: 100% !important;
              max-width: 280px !important;
              justify-content: center !important;
            }

            .info-section {
              padding: 3rem 3% !important;
            }

            .section-title {
              font-size: 1.8rem !important;
            }

            .features-grid {
              grid-template-columns: 1fr !important;
              gap: 1rem !important;
            }

            .feature-item {
              padding: 1rem !important;
              padding-left: 2.5rem !important;
            }
          }

          @media (max-width: 480px) {
            .hero-title {
              font-size: 1.8rem !important;
              letter-spacing: 1px !important;
            }

            .hero-subtitle {
              font-size: 0.95rem !important;
            }

            .cta-button {
              font-size: 0.9rem !important;
              padding: 0.7rem 1.5rem !important;
            }

            .info-section {
              padding: 2rem 4% !important;
            }

            .section-title {
              font-size: 1.6rem !important;
            }
          }
        `,
        }}
      />
    </div>
  );
};

// Inline Styles Object
const styles = {
  body: {
    margin: 0,
    padding: 0,
    boxSizing: "border-box" as const,
    fontFamily: "'Arial', sans-serif",
    color: "#333",
    lineHeight: 1.6,
  },
  hero: {
    height: "100vh",
    display: "flex",
    flexDirection: "column" as const,
    justifyContent: "center",
    alignItems: "center",
    textAlign: "center" as const,
    padding: "0 5%",
    background:
      'linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url("https://images.unsplash.com/photo-1546519638-68e109498ffc?ixlib=rb-1.2.1&auto=format&fit=crop&w=1920&q=80") no-repeat center center/cover',
    color: "white",
  },
  heroH2: {
    fontSize: "3rem",
    marginBottom: "1.5rem",
    animation: "fadeIn 1s ease-in-out",
    textTransform: "uppercase" as const,
    letterSpacing: "2px",
  },
  heroP: {
    fontSize: "1.2rem",
    maxWidth: "700px",
    marginBottom: "2.5rem",
  },
  ctaButtons: {
    display: "flex",
    gap: "1.5rem",
    flexWrap: "wrap" as const,
    justifyContent: "center",
  },
  btn: {
    display: "inline-flex",
    alignItems: "center",
    justifyContent: "center",
    padding: "0.8rem 1.8rem",
    borderRadius: "30px",
    fontWeight: "bold" as const,
    transition: "all 0.3s",
    textDecoration: "none",
    textAlign: "center" as const,
  },
  icon: {
    marginRight: "8px",
  },
  btnPrimary: {
    backgroundColor: "#e74c3c",
    color: "white",
  },
  btnSecondary: {
    backgroundColor: "#e74c3c",
    color: "white",
  },
  btnTertiary: {
    backgroundColor: "#e74c3c",
    color: "white",
  },
  infoSection: {
    padding: "5rem 5%",
    maxWidth: "1200px",
    margin: "0 auto",
  },
  sectionTitle: {
    fontSize: "2.2rem",
    color: "#2c3e50",
    marginBottom: "2rem",
    textAlign: "center" as const,
  },
  ul: {
    listStyleType: "none",
    display: "grid",
    gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
    gap: "1.5rem",
  },
  li: {
    backgroundColor: "#f8f9fa",
    padding: "1.5rem",
    borderRadius: "8px",
    boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
    transition: "transform 0.3s",
    position: "relative" as const,
    paddingLeft: "3rem",
  },
  footer: {
    textAlign: "center" as const,
    padding: "2rem",
    backgroundColor: "#2c3e50",
    color: "white",
  },
};

export default HomePage;

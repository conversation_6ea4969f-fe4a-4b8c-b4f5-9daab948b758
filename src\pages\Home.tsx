import React from "react";
import { Link } from "react-router-dom";

const HomePage = () => {
  return (
    <div style={styles.body}>
      {/* Hero Section */}
      <section style={styles.hero}>
        <h2 style={styles.heroH2}>Train Smarter. Play Harder.</h2>
        <p style={styles.heroP}>
          Connect with professional coaches, track your progress, and explore
          verified sports gear.
        </p>
        <div style={styles.ctaButtons}>
          <Link
            to="/join-as-athlete"
            className="btn primary"
            style={{ ...styles.btn, ...styles.btnPrimary }}
          >
            <i className="fas fa-user" style={styles.icon}></i> Join as Athlete
          </Link>
          <Link
            to="/become-a-coach"
            className="btn secondary"
            style={{ ...styles.btn, ...styles.btnSecondary }}
          >
            <i className="fas fa-chalkboard-teacher" style={styles.icon}></i>{" "}
            Become a Coach
          </Link>
          <Link
            to="/join-as-vendor"
            className="btn tertiary"
            style={{ ...styles.btn, ...styles.btnTertiary }}
          >
            <i className="fas fa-store" style={styles.icon}></i> Join as Vendor
          </Link>
        </div>
      </section>

      {/* About Section */}
      <section style={styles.infoSection} id="about">
        <h3 style={styles.sectionTitle}>Why Choose Sport Sphere?</h3>
        <ul style={styles.ul}>
          {[
            "Personalized coaching sessions with certified professionals",
            "Real-time progress tracking with advanced analytics",
            "Secure payment processing and encrypted messaging",
            "Authentic sports gear marketplace with verified vendors",
            "Comprehensive athlete development programs",
            "Flexible scheduling and session management",
          ].map((item, index) => (
            <li key={index} style={styles.li}>
              {item}
            </li>
          ))}
        </ul>
      </section>

      {/* Footer */}
      <footer style={styles.footer}>
        <p>&copy; 2025 Sport Sphere | All rights reserved</p>
      </footer>

      {/* Scoped Styles */}
      <style
        dangerouslySetInnerHTML={{
          __html: `
          @import url("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css");

          /* Animations */
          @keyframes fadeIn {
            from {
              opacity: 0;
              transform: translateY(20px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }
        `,
        }}
      />
    </div>
  );
};

// Inline Styles Object
const styles = {
  body: {
    margin: 0,
    padding: 0,
    boxSizing: "border-box" as const,
    fontFamily: "'Arial', sans-serif",
    color: "#333",
    lineHeight: 1.6,
  },
  hero: {
    height: "100vh",
    display: "flex",
    flexDirection: "column" as const,
    justifyContent: "center",
    alignItems: "center",
    textAlign: "center" as const,
    padding: "0 5%",
    background:
      'linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url("https://images.unsplash.com/photo-1546519638-68e109498ffc?ixlib=rb-1.2.1&auto=format&fit=crop&w=1920&q=80") no-repeat center center/cover',
    color: "white",
  },
  heroH2: {
    fontSize: "3rem",
    marginBottom: "1.5rem",
    animation: "fadeIn 1s ease-in-out",
    textTransform: "uppercase" as const,
    letterSpacing: "2px",
  },
  heroP: {
    fontSize: "1.2rem",
    maxWidth: "700px",
    marginBottom: "2.5rem",
  },
  ctaButtons: {
    display: "flex",
    gap: "1.5rem",
    flexWrap: "wrap" as const,
    justifyContent: "center",
  },
  btn: {
    display: "inline-flex",
    alignItems: "center",
    justifyContent: "center",
    padding: "0.8rem 1.8rem",
    borderRadius: "30px",
    fontWeight: "bold" as const,
    transition: "all 0.3s",
    textDecoration: "none",
    textAlign: "center" as const,
  },
  icon: {
    marginRight: "8px",
  },
  btnPrimary: {
    backgroundColor: "#e74c3c",
    color: "white",
  },
  btnSecondary: {
    backgroundColor: "#e74c3c",
    color: "white",
  },
  btnTertiary: {
    backgroundColor: "#e74c3c",
    color: "white",
  },
  infoSection: {
    padding: "5rem 5%",
    maxWidth: "1200px",
    margin: "0 auto",
  },
  sectionTitle: {
    fontSize: "2.2rem",
    color: "#2c3e50",
    marginBottom: "2rem",
    textAlign: "center" as const,
  },
  ul: {
    listStyleType: "none",
    display: "grid",
    gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
    gap: "1.5rem",
  },
  li: {
    backgroundColor: "#f8f9fa",
    padding: "1.5rem",
    borderRadius: "8px",
    boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
    transition: "transform 0.3s",
    position: "relative" as const,
    paddingLeft: "3rem",
  },
  footer: {
    textAlign: "center" as const,
    padding: "2rem",
    backgroundColor: "#2c3e50",
    color: "white",
  },
};

export default HomePage;

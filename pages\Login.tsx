const loginStyles = `
  /* Reset and base styles */
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: "Arial", sans-serif;
  }

  body {
    color: #333;
    line-height: 1.6;
    background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)),
      url("https://images.unsplash.com/photo-1546519638-68e109498ffc?ixlib=rb-1.2.1&auto=format&fit=crop&w=1920&q=80")
        no-repeat center center/cover;
    min-height: 100vh;
  }

  a {
    text-decoration: none;
    color: inherit;
  }



  /* Form page styles */
  .form-page {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 2rem;
  }

  .form-page h2 {
    font-size: 2.2rem;
    color: white;
    margin-bottom: 2rem;
    text-align: center;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
  }

  form {
    background: rgba(255, 255, 255, 0.95);
    padding: 2.5rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    width: 100%;
    max-width: 450px;
  }

  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
  }

  input {
    width: 100%;
    padding: 0.8rem 1rem;
    margin-bottom: 1.5rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: border 0.3s;
  }

  input:focus {
    outline: none;
    border-color: #e74c3c;
  }

  button[type="submit"] {
    width: 100%;
    padding: 1rem;
    background-color: #e74c3c;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s, transform 0.2s;
  }

  button[type="submit"]:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
  }

  .btn {
    display: inline-block;
    padding: 0.6rem 1.5rem;
    border-radius: 30px;
    font-weight: bold;
    transition: all 0.3s;
  }

  .btn.secondary {
    background-color: transparent;
    border: 2px solid white;
  }

  .btn.secondary:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  form p {
    text-align: center;
    margin-top: 1.5rem;
    color: #7f8c8d;
  }

  form p a {
    color: #e74c3c;
    font-weight: 600;
    transition: color 0.3s;
  }

  form p a:hover {
    color: #c0392b;
    text-decoration: underline;
  }

  /* Footer styles */
  footer {
    text-align: center;
    padding: 1.5rem;
    background-color: rgba(44, 62, 80, 0.9);
    color: white;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .form-page {
      padding: 1rem;
      min-height: 100vh;
    }

    form {
      padding: 1.5rem;
      margin: 0 0.5rem;
    }

    .form-page h2 {
      font-size: 2rem;
    }
  }

  @media (max-width: 480px) {
    .form-page {
      padding: 0.5rem;
    }

    form {
      padding: 1rem;
      margin: 0;
    }

    .form-page h2 {
      font-size: 1.8rem;
    }

    .btn.secondary {
      padding: 0.5rem 1rem;
      font-size: 0.9rem;
    }

    input {
      font-size: 16px; /* Prevents zoom on iOS */
    }
  }
`;

const Login = () => {
  return (
    <div>
      {/* Inject CSS dynamically */}
      <style dangerouslySetInnerHTML={{ __html: loginStyles }} />

      <main className="form-page">
        <h2>Welcome Back!</h2>
        <form>
          <label htmlFor="email">Email</label>
          <input
            type="email"
            id="email"
            placeholder="Enter your email"
            required
          />

          <label htmlFor="password">Password</label>
          <input
            type="password"
            id="password"
            placeholder="Enter your password"
            required
          />

          <button type="submit">Login</button>
          <p>
            Don't have an account? <a href="/register">Sign up</a> or
            <a href="#"> Forgot Password?</a>
          </p>
        </form>
      </main>

      <footer>
        <p>&copy; 2025 Sport Sphere | All rights reserved</p>
      </footer>
    </div>
  );
};

export default Login;

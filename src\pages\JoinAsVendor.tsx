const JoinAsVendor = () => {
  return (
    <div style={styles.container}>
      <div style={styles.content}>
        <div style={styles.header}>
          <h1 style={styles.title}>Join as Vendor</h1>
          <p style={styles.subtitle}>
            Sell your sports equipment and gear to athletes worldwide
          </p>
        </div>

        <div style={styles.card}>
          <form style={styles.form}>
            <div style={styles.formGroup}>
              <label style={styles.label}>Business Name</label>
              <input
                type="text"
                style={styles.input}
                placeholder="Enter your business name"
                required
              />
            </div>

            <div style={styles.formGroup}>
              <label style={styles.label}>Contact Person</label>
              <input
                type="text"
                style={styles.input}
                placeholder="Enter contact person name"
                required
              />
            </div>

            <div style={styles.formGroup}>
              <label style={styles.label}>Email</label>
              <input
                type="email"
                style={styles.input}
                placeholder="Enter business email"
                required
              />
            </div>

            <div style={styles.formGroup}>
              <label style={styles.label}>Phone Number</label>
              <input
                type="tel"
                style={styles.input}
                placeholder="Enter business phone number"
                required
              />
            </div>

            <div style={styles.formGroup}>
              <label style={styles.label}>Business Address</label>
              <textarea
                style={styles.textarea}
                placeholder="Enter your business address..."
                rows={3}
                required
              />
            </div>

            <div style={styles.formGroup}>
              <label style={styles.label}>Product Categories</label>
              <select style={styles.input} required>
                <option value="">Select main product category</option>
                <option value="equipment">Sports Equipment</option>
                <option value="apparel">Sports Apparel</option>
                <option value="footwear">Sports Footwear</option>
                <option value="accessories">Sports Accessories</option>
                <option value="nutrition">Sports Nutrition</option>
                <option value="technology">Sports Technology</option>
                <option value="other">Other</option>
              </select>
            </div>

            <div style={styles.formGroup}>
              <label style={styles.label}>Business Type</label>
              <select style={styles.input} required>
                <option value="">Select business type</option>
                <option value="manufacturer">Manufacturer</option>
                <option value="retailer">Retailer</option>
                <option value="distributor">Distributor</option>
                <option value="individual">Individual Seller</option>
              </select>
            </div>

            <div style={styles.formGroup}>
              <label style={styles.label}>Years in Business</label>
              <select style={styles.input} required>
                <option value="">Select experience</option>
                <option value="new">New Business</option>
                <option value="1-2">1-2 years</option>
                <option value="3-5">3-5 years</option>
                <option value="6-10">6-10 years</option>
                <option value="10+">10+ years</option>
              </select>
            </div>

            <div style={styles.formGroup}>
              <label style={styles.label}>Business Description</label>
              <textarea
                style={styles.textarea}
                placeholder="Tell us about your business and products..."
                rows={4}
              />
            </div>

            <button type="submit" style={styles.submitBtn}>
              Apply as Vendor
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

const styles = {
  container: {
    minHeight: "100vh",
    background: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
    padding: "2rem",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
  },
  content: {
    width: "100%",
    maxWidth: "600px",
  },
  header: {
    textAlign: "center" as const,
    marginBottom: "2rem",
    color: "white",
  },
  title: {
    fontSize: "2.5rem",
    fontWeight: "bold" as const,
    marginBottom: "0.5rem",
    textShadow: "0 2px 4px rgba(0,0,0,0.3)",
  },
  subtitle: {
    fontSize: "1.2rem",
    opacity: 0.9,
  },
  card: {
    backgroundColor: "white",
    borderRadius: "12px",
    padding: "2rem",
    boxShadow: "0 10px 30px rgba(0,0,0,0.2)",
  },
  form: {
    display: "flex",
    flexDirection: "column" as const,
    gap: "1.5rem",
  },
  formGroup: {
    display: "flex",
    flexDirection: "column" as const,
    gap: "0.5rem",
  },
  label: {
    fontSize: "1rem",
    fontWeight: "600" as const,
    color: "#333",
  },
  input: {
    padding: "0.75rem",
    border: "2px solid #e1e5e9",
    borderRadius: "8px",
    fontSize: "1rem",
    transition: "border-color 0.3s ease",
    outline: "none",
  },
  textarea: {
    padding: "0.75rem",
    border: "2px solid #e1e5e9",
    borderRadius: "8px",
    fontSize: "1rem",
    transition: "border-color 0.3s ease",
    outline: "none",
    resize: "vertical" as const,
    fontFamily: "inherit",
  },
  submitBtn: {
    backgroundColor: "#4facfe",
    color: "white",
    padding: "1rem 2rem",
    border: "none",
    borderRadius: "8px",
    fontSize: "1.1rem",
    fontWeight: "600" as const,
    cursor: "pointer",
    transition: "background-color 0.3s ease",
    marginTop: "1rem",
  },
};

// Add hover effects
const hoverStyles = `
  input:focus, textarea:focus, select:focus {
    border-color: #4facfe !important;
    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1) !important;
  }
  
  button:hover {
    background-color: #3d8bfe !important;
    transform: translateY(-2px);
  }
`;

// Inject styles
if (typeof document !== "undefined") {
  const styleElement = document.createElement("style");
  styleElement.textContent = hoverStyles;
  document.head.appendChild(styleElement);
}

export default JoinAsVendor;

import React from "react";

const registerStyles = `
  /* Reset and base styles */
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Arial', sans-serif;
  }

  body {
    color: #333;
    line-height: 1.6;
    background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), 
                url('https://images.unsplash.com/photo-1546519638-68e109498ffc?ixlib=rb-1.2.1&auto=format&fit=crop&w=1920&q=80') no-repeat center center/cover;
    min-height: 100vh;
  }

  a {
    text-decoration: none;
    color: inherit;
  }

  /* Header styles - matching previous pages */
  header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 5%;
    background-color: rgba(255, 255, 255, 0.9);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 100;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .logo {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .logo-img {
    height: 40px;
    width: auto;
  }

  .logo-text {
    font-size: 1.8rem;
    font-weight: bold;
    color: #2c3e50;   
    font-style: italic;
  }

  nav {
    display: flex;
    gap: 1.5rem;
    align-items: center;
  }

  nav a {
    font-weight: 600;
    transition: color 0.3s;
  }

  nav a:hover {
    color: #e74c3c;
  }

  /* Form page styles */
  .form-page {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 2rem;
    padding-top: 120px; /* Account for fixed header */
  }

  .form-page h2 {
    font-size: 2.2rem;
    color: white;
    margin-bottom: 2rem;
    text-align: center;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
  }

  form {
    background: rgba(255, 255, 255, 0.95);
    padding: 2.5rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    width: 100%;
    max-width: 500px;
  }

  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
  }

  input, select {
    width: 100%;
    padding: 0.8rem 1rem;
    margin-bottom: 1.5rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: border 0.3s;
  }

  input:focus, select:focus {
    outline: none;
    border-color: #e74c3c;
  }

  button[type="submit"] {
    width: 100%;
    padding: 1rem;
    background-color: #e74c3c;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s, transform 0.2s;
  }

  button[type="submit"]:hover {
    background-color: #c0392b;
    transform: translateY(-2px);
  }

  .btn {
    display: inline-block;
    padding: 0.6rem 1.5rem;
    border-radius: 30px;
    font-weight: bold;
    transition: all 0.3s;
  }

  .btn.secondary {
    background-color: transparent;
    border: 2px solid white;
  }

  .btn.secondary:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  /* Footer styles */
  footer {
    text-align: center;
    padding: 1.5rem;
    background-color: rgba(44, 62, 80, 0.9);
    color: white;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    header {
      padding: 1rem 5%;
    }

    .logo-img {
      height: 30px;
    }

    .logo-text {
      font-size: 1.5rem;
    }

    .form-page {
      padding: 1.5rem;
      padding-top: 100px;
    }

    form {
      padding: 1.5rem;
    }

    nav {
      gap: 1rem;
    }
  }

  @media (max-width: 480px) {
    .logo {
      flex-direction: column;
      align-items: flex-start;
    }

    .logo-img {
      height: 25px;
    }

    .logo-text {
      font-size: 1.2rem;
    }

    .form-page h2 {
      font-size: 1.8rem;
    }
  }
`;

const Register = () => {
  return (
    <div>
      {/* Inject CSS dynamically */}
      <style dangerouslySetInnerHTML={{ __html: registerStyles }} />

      <header>
        <a href="/index.html" className="logo">
          <img
            src="/assets/images/Logo.png"
            alt="Sport Sphere Logo"
            className="logo-img"
          />
          <div>
            <div className="logo-text">Sports Sphere</div>
          </div>
        </a>
        <nav>
          <a href="/login" className="btn secondary">
            Login
          </a>
        </nav>
      </header>

      <main className="form-page">
        <h2>Create Your Account</h2>
        <form>
          <label htmlFor="name">Name</label>
          <input type="text" id="name" placeholder="Your Name" required />

          <label htmlFor="email">Email</label>
          <input type="email" id="email" placeholder="Email address" required />

          <label htmlFor="password">Password</label>
          <input
            type="password"
            id="password"
            placeholder="Create a password"
            required
          />

          <label htmlFor="role">Role</label>
          <select id="role">
            <option value="athlete">Athlete</option>
            <option value="coach">Coach</option>
            <option value="vendor">Vendor</option>
          </select>

          <label htmlFor="sport">Sport Specialization</label>
          <input type="text" id="sport" placeholder="e.g. Football, Tennis" />

          <label htmlFor="experience">Experience (Coaches)</label>
          <input type="text" id="experience" placeholder="e.g. 3 years" />

          <button type="submit">Register</button>
          <p
            style={{
              textAlign: "center",
              marginTop: "1.5rem",
              color: "#7f8c8d",
            }}
          >
            Already have an account?{" "}
            <a href="/login" style={{ color: "#e74c3c", fontWeight: "600" }}>
              Login here
            </a>
          </p>
        </form>
      </main>

      <footer>
        <p>&copy; 2025 Sport Sphere | All rights reserved</p>
      </footer>
    </div>
  );
};

export default Register;

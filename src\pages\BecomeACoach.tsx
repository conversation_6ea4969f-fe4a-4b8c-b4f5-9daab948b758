const BecomeACoach = () => {
  return (
    <div style={styles.container} className="coach-container">
      <div style={styles.content} className="coach-content">
        <div style={styles.header}>
          <h1 style={styles.title} className="coach-title">
            Become a Coach
          </h1>
          <p style={styles.subtitle} className="coach-subtitle">
            Share your expertise and help athletes reach their potential
          </p>
        </div>

        <div style={styles.card} className="coach-card">
          <form style={styles.form} className="coach-form">
            <div style={styles.formGroup}>
              <label style={styles.label}>Full Name</label>
              <input
                type="text"
                style={styles.input}
                placeholder="Enter your full name"
                required
              />
            </div>

            <div style={styles.formGroup}>
              <label style={styles.label}>Email</label>
              <input
                type="email"
                style={styles.input}
                placeholder="Enter your email"
                required
              />
            </div>

            <div style={styles.formGroup}>
              <label style={styles.label}>Phone Number</label>
              <input
                type="tel"
                style={styles.input}
                placeholder="Enter your phone number"
                required
              />
            </div>

            <div style={styles.formGroup}>
              <label style={styles.label}>Coaching Specialization</label>
              <select style={styles.input} required>
                <option value="">Select your specialization</option>
                <option value="tennis">Tennis</option>
                <option value="basketball">Basketball</option>
                <option value="soccer">Soccer</option>
                <option value="swimming">Swimming</option>
                <option value="golf">Golf</option>
                <option value="volleyball">Volleyball</option>
                <option value="cycling">Cycling</option>
                <option value="fitness">General Fitness</option>
                <option value="other">Other</option>
              </select>
            </div>

            <div style={styles.formGroup}>
              <label style={styles.label}>Years of Experience</label>
              <select style={styles.input} required>
                <option value="">Select experience level</option>
                <option value="1-2">1-2 years</option>
                <option value="3-5">3-5 years</option>
                <option value="6-10">6-10 years</option>
                <option value="10+">10+ years</option>
              </select>
            </div>

            <div style={styles.formGroup}>
              <label style={styles.label}>Certifications</label>
              <input
                type="text"
                style={styles.input}
                placeholder="List your coaching certifications"
              />
            </div>

            <div style={styles.formGroup}>
              <label style={styles.label}>Coaching Philosophy</label>
              <textarea
                style={styles.textarea}
                placeholder="Describe your coaching approach and philosophy..."
                rows={4}
              />
            </div>

            <div style={styles.formGroup}>
              <label style={styles.label}>Hourly Rate (USD)</label>
              <input
                type="number"
                style={styles.input}
                placeholder="Enter your hourly rate"
                min="10"
                max="500"
              />
            </div>

            <button type="submit" style={styles.submitBtn}>
              Apply as Coach
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

const styles = {
  container: {
    minHeight: "100vh",
    background: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
    padding: "2rem",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
  },
  content: {
    width: "100%",
    maxWidth: "600px",
  },
  header: {
    textAlign: "center" as const,
    marginBottom: "2rem",
    color: "white",
  },
  title: {
    fontSize: "2.5rem",
    fontWeight: "bold" as const,
    marginBottom: "0.5rem",
    textShadow: "0 2px 4px rgba(0,0,0,0.3)",
  },
  subtitle: {
    fontSize: "1.2rem",
    opacity: 0.9,
  },
  card: {
    backgroundColor: "white",
    borderRadius: "12px",
    padding: "2rem",
    boxShadow: "0 10px 30px rgba(0,0,0,0.2)",
  },
  form: {
    display: "flex",
    flexDirection: "column" as const,
    gap: "1.5rem",
  },
  formGroup: {
    display: "flex",
    flexDirection: "column" as const,
    gap: "0.5rem",
  },
  label: {
    fontSize: "1rem",
    fontWeight: "600" as const,
    color: "#333",
  },
  input: {
    padding: "0.75rem",
    border: "2px solid #e1e5e9",
    borderRadius: "8px",
    fontSize: "1rem",
    transition: "border-color 0.3s ease",
    outline: "none",
  },
  textarea: {
    padding: "0.75rem",
    border: "2px solid #e1e5e9",
    borderRadius: "8px",
    fontSize: "1rem",
    transition: "border-color 0.3s ease",
    outline: "none",
    resize: "vertical" as const,
    fontFamily: "inherit",
  },
  submitBtn: {
    backgroundColor: "#f5576c",
    color: "white",
    padding: "1rem 2rem",
    border: "none",
    borderRadius: "8px",
    fontSize: "1.1rem",
    fontWeight: "600" as const,
    cursor: "pointer",
    transition: "background-color 0.3s ease",
    marginTop: "1rem",
  },
};

const hoverStyles = `
  input:focus, textarea:focus, select:focus {
    border-color: #f5576c !important;
    box-shadow: 0 0 0 3px rgba(245, 87, 108, 0.1) !important;
  }

  button:hover {
    background-color: #f04458 !important;
    transform: translateY(-2px);
  }

  @media (max-width: 768px) {
    .coach-container {
      padding: 1rem !important;
    }

    .coach-content {
      max-width: 100% !important;
    }

    .coach-card {
      padding: 1.5rem !important;
      margin: 0 0.5rem !important;
    }

    .coach-title {
      font-size: 2rem !important;
    }

    .coach-subtitle {
      font-size: 1rem !important;
    }
  }

  @media (max-width: 480px) {
    .coach-container {
      padding: 0.5rem !important;
    }

    .coach-card {
      padding: 1rem !important;
      margin: 0 !important;
    }

    .coach-title {
      font-size: 1.8rem !important;
    }

    .coach-subtitle {
      font-size: 0.95rem !important;
    }

    .coach-form {
      gap: 1rem !important;
    }

    input, textarea, select {
      font-size: 16px !important; /* Prevents zoom on iOS */
    }
  }
`;

if (typeof document !== "undefined") {
  const styleElement = document.createElement("style");
  styleElement.textContent = hoverStyles;
  document.head.appendChild(styleElement);
}

export default BecomeACoach;
